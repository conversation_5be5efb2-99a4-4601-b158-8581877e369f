import sys
import os
import time
import threading
import queue
import csv
from datetime import datetime
import serial
import serial.tools.list_ports
import struct
import numpy as np

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import pyqtgraph as pg

# 导入协议解析器和管理器
from protocol_parser import ProtocolParser
from protocol_manager import ProtocolEditor, ProtocolManager

# 配置
DEFAULT_PROTOCOL_FILE = "protocol_definition.csv"
BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]
FRAME_HEADER = b'\xaa\x55'

# 全局协议解析器实例
protocol_parser = ProtocolParser()
if os.path.exists(DEFAULT_PROTOCOL_FILE):
    protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)

class Protocol422Receiver:
    """422通讯接收端核心逻辑类"""
    def __init__(self, port=None, baudrate=19200, timeout=0.1):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.ser = None
        self.buffer = bytearray()
        self.data_queue = queue.Queue(maxsize=200)
        self.running = False
        self.time_window = 30.0  # 显示最近30秒的数据
        self.data_history = {}
        self.all_data_with_timestamp = []
        self.time_history = []
        self.start_time = None
        self.update_protocol_structure()

    def update_protocol_structure(self):
        self.protocol_structure = protocol_parser.get_protocol_structure()
        self.frame_length = self._calculate_frame_length()
        self._init_data_history()
        print(f"接收端协议已更新。帧长度: {self.frame_length}字节。")

    def _calculate_frame_length(self):
        if not self.protocol_structure: return 0
        return self.protocol_structure[-1]['byte_indices'][-1] + 1

    def _init_data_history(self):
        self.data_history.clear()
        self.all_data_with_timestamp.clear()
        self.time_history.clear()
        self.start_time = None
        for field in self.protocol_structure:
            if field['name'] not in ['帧头', '校验和']:
                self.data_history[field['name']] = []

    def connect(self, port, baudrate):
        try:
            if self.ser and self.ser.is_open: self.ser.close()
            self.port, self.baudrate = port, baudrate
            self.ser = serial.Serial(port=port, baudrate=baudrate, timeout=self.timeout)
            self.buffer.clear()
            return True, f"已连接到 {port}, 波特率 {baudrate}"
        except serial.SerialException as e:
            return False, f"串口连接失败: {e}"

    def start(self):
        if self.running: return False, "已在运行"
        if not self.ser or not self.ser.is_open: return False, "串口未连接"
        self.running = True
        self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
        self.receive_thread.start()
        return True, "开始接收数据"

    def stop(self):
        self.running = False
        if hasattr(self, 'receive_thread'): self.receive_thread.join(timeout=1.0)
        if self.ser and self.ser.is_open: self.ser.close()
        return True, "已停止接收"

    def _receive_loop(self):
        while self.running:
            try:
                if self.ser.in_waiting > 0:
                    self.buffer.extend(self.ser.read(self.ser.in_waiting))
                    self._process_buffer()
                else:
                    time.sleep(0.001)
            except Exception as e:
                print(f"接收循环错误: {e}")
                self.stop()

    def _process_buffer(self):
        while len(self.buffer) >= self.frame_length:
            frame_start = self.buffer.find(FRAME_HEADER)
            if frame_start == -1:
                self.buffer = self.buffer[-(len(FRAME_HEADER) - 1):]
                break
            if frame_start > 0: self.buffer = self.buffer[frame_start:]
            if len(self.buffer) < self.frame_length: break

            raw_frame = self.buffer[:self.frame_length]
            checksum_field = protocol_parser.get_field_by_name('校验和')
            is_valid = False
            if not checksum_field:
                is_valid = True
            else:
                checksum_idx = checksum_field['byte_indices'][0]
                received_checksum = raw_frame[checksum_idx]
                calculated_checksum = 0
                for i in range(2, checksum_idx):
                    calculated_checksum ^= raw_frame[i]
                is_valid = (calculated_checksum == received_checksum)

            if is_valid:
                parsed_data = self._parse_frame(raw_frame)
                self._update_data_history(parsed_data)
                self._save_data_with_timestamp(parsed_data)
                if not self.data_queue.full():
                    self.data_queue.put({'raw': raw_frame, 'parsed': parsed_data})
                self.buffer = self.buffer[self.frame_length:]
            else:
                self.buffer = self.buffer[1:]

    def _parse_frame(self, raw_frame):
        parsed_data = {}
        for field in self.protocol_structure:
            name, start_idx, data_type = field['name'], field['byte_indices'][0], field['type']
            try:
                if data_type == 'int16':
                    value = struct.unpack_from('<h', raw_frame, start_idx)[0]
                elif data_type == 'uint16':
                    value = struct.unpack_from('<H', raw_frame, start_idx)[0]
                elif data_type == 'int8':
                    value = struct.unpack_from('<b', raw_frame, start_idx)[0]
                elif data_type == 'uint8':
                    value = struct.unpack_from('<B', raw_frame, start_idx)[0]
                else:
                    value = raw_frame[start_idx]
                parsed_data[name] = value
            except struct.error as e:
                parsed_data[name] = "Error"
        return parsed_data

    def _update_data_history(self, parsed_data):
        current_time = time.time()
        if self.start_time is None:
            self.start_time = current_time

        relative_time = current_time - self.start_time
        self.time_history.append(relative_time)

        for name, value in parsed_data.items():
            if name in self.data_history and isinstance(value, (int, float)):
                self.data_history[name].append(value)

        self._cleanup_old_data(relative_time)

    def _cleanup_old_data(self, current_time):
        if not self.time_history:
            return

        window_start_time = current_time - self.time_window
        keep_from_index = 0
        for i, t in enumerate(self.time_history):
            if t >= window_start_time:
                keep_from_index = i
                break

        if keep_from_index > 0:
            self.time_history = self.time_history[keep_from_index:]
            for name in self.data_history:
                if len(self.data_history[name]) > keep_from_index:
                    self.data_history[name] = self.data_history[name][keep_from_index:]

    def _save_data_with_timestamp(self, parsed_data):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        data_row = {'时间戳': timestamp}
        data_row.update(parsed_data)
        self.all_data_with_timestamp.append(data_row)

    def get_latest_data(self):
        try:
            all_data = []
            while not self.data_queue.empty():
                all_data.append(self.data_queue.get_nowait())
            return all_data
        except queue.Empty:
            return None

    def save_all_data_to_csv(self, file_path):
        if not self.all_data_with_timestamp:
            return False, "没有数据可保存"

        try:
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['时间戳']
                if self.all_data_with_timestamp:
                    first_row = self.all_data_with_timestamp[0]
                    for key in first_row.keys():
                        if key != '时间戳':
                            fieldnames.append(key)

                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.all_data_with_timestamp)

            return True, f"成功保存 {len(self.all_data_with_timestamp)} 条数据到 {file_path}"
        except Exception as e:
            return False, f"保存失败: {str(e)}"


class ReceiverWidget(QWidget):
    """接收端界面组件"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.receiver = Protocol422Receiver()
        self.data_vars = {}
        self.plot_widgets = {}
        self.table_item_ids = {}
        self.colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
        
        self.init_ui()
        self.update_port_list()
        
        # 定时器用于更新数据
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_loop)
        self.update_timer.start(100)  # 100ms更新一次

    def init_ui(self):
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_group = QGroupBox("控制面板")
        control_layout = QHBoxLayout(control_group)
        
        # 串口设置
        control_layout.addWidget(QLabel("串口:"))
        self.port_combo = QComboBox()
        control_layout.addWidget(self.port_combo)
        
        control_layout.addWidget(QLabel("波特率:"))
        self.baudrate_combo = QComboBox()
        self.baudrate_combo.addItems([str(b) for b in BAUDRATES])
        self.baudrate_combo.setCurrentText("19200")
        control_layout.addWidget(self.baudrate_combo)
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.update_port_list)
        control_layout.addWidget(refresh_btn)
        
        # 控制按钮
        self.start_btn = QPushButton("启动接收")
        self.start_btn.clicked.connect(self.start_receiver)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止接收")
        self.stop_btn.clicked.connect(self.stop_receiver)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(self.clear_data)
        control_layout.addWidget(clear_btn)
        
        save_btn = QPushButton("保存数据")
        save_btn.clicked.connect(self.save_data_to_csv)
        control_layout.addWidget(save_btn)
        
        # 时间窗口设置
        control_layout.addWidget(QLabel("时间窗口:"))
        self.time_window_spin = QSpinBox()
        self.time_window_spin.setRange(1, 300)
        self.time_window_spin.setValue(30)
        self.time_window_spin.setSuffix("秒")
        control_layout.addWidget(self.time_window_spin)
        
        apply_btn = QPushButton("应用")
        apply_btn.clicked.connect(self.apply_time_window)
        control_layout.addWidget(apply_btn)
        
        layout.addWidget(control_group)
        
        # 主要内容区域
        main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 数据选择
        select_group = QGroupBox("绘图数据选择")
        select_layout = QVBoxLayout(select_group)
        
        # 全选/全不选按钮
        btn_layout = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(lambda: self.toggle_all_checkboxes(True))
        btn_layout.addWidget(select_all_btn)
        
        select_none_btn = QPushButton("全不选")
        select_none_btn.clicked.connect(lambda: self.toggle_all_checkboxes(False))
        btn_layout.addWidget(select_none_btn)
        select_layout.addLayout(btn_layout)
        
        # 复选框滚动区域
        self.checkbox_scroll = QScrollArea()
        self.checkbox_widget = QWidget()
        self.checkbox_layout = QVBoxLayout(self.checkbox_widget)
        self.checkbox_scroll.setWidget(self.checkbox_widget)
        self.checkbox_scroll.setWidgetResizable(True)
        select_layout.addWidget(self.checkbox_scroll)
        
        left_layout.addWidget(select_group)
        
        # 当前帧数据表格
        table_group = QGroupBox("当前帧数据")
        table_layout = QVBoxLayout(table_group)
        
        self.data_table = QTableWidget()
        self.data_table.setColumnCount(3)
        self.data_table.setHorizontalHeaderLabels(["字段", "数值", "十六进制"])
        self.data_table.horizontalHeader().setStretchLastSection(True)
        table_layout.addWidget(self.data_table)
        
        left_layout.addWidget(table_group)
        
        main_splitter.addWidget(left_panel)

        # 右侧绘图区域
        self.plot_scroll = QScrollArea()
        self.plot_widget = QWidget()
        self.plot_layout = QVBoxLayout(self.plot_widget)
        self.plot_scroll.setWidget(self.plot_widget)
        self.plot_scroll.setWidgetResizable(True)

        main_splitter.addWidget(self.plot_scroll)
        main_splitter.setSizes([250, 650])  # 调整比例，左侧更窄
        
        layout.addWidget(main_splitter)
        
        # 状态栏
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
        
        self.rebuild_ui_from_protocol()

    def rebuild_ui_from_protocol(self):
        """根据协议重建UI"""
        # 清除现有复选框
        for i in reversed(range(self.checkbox_layout.count())):
            self.checkbox_layout.itemAt(i).widget().setParent(None)
        self.data_vars.clear()

        # 重建复选框
        for field in self.receiver.protocol_structure:
            name = field['name']
            if name in ['帧头', '校验和']: continue

            checkbox = QCheckBox(name)
            checkbox.stateChanged.connect(self.rebuild_plot_area)
            self.checkbox_layout.addWidget(checkbox)
            self.data_vars[name] = checkbox

        # 重建数据表格
        self.data_table.setRowCount(len(self.receiver.protocol_structure))
        self.table_item_ids.clear()

        for i, field in enumerate(self.receiver.protocol_structure):
            name = field['name']
            self.data_table.setItem(i, 0, QTableWidgetItem(name))
            self.data_table.setItem(i, 1, QTableWidgetItem("-"))
            self.data_table.setItem(i, 2, QTableWidgetItem("-"))
            self.table_item_ids[name] = i

        self.clear_data()

    def toggle_all_checkboxes(self, state):
        for checkbox in self.data_vars.values():
            checkbox.setChecked(state)

    def rebuild_plot_area(self):
        """重建绘图区域"""
        # 清除现有绘图
        for i in reversed(range(self.plot_layout.count())):
            item = self.plot_layout.itemAt(i)
            if item.widget():
                item.widget().setParent(None)
        self.plot_widgets.clear()

        # 获取选中的字段
        selected_fields = [name for name, checkbox in self.data_vars.items() if checkbox.isChecked()]

        if not selected_fields:
            return

        # 创建合并视图
        combined_plot = pg.PlotWidget(title="合并视图")
        combined_plot.setLabel('left', '数值')
        combined_plot.setLabel('bottom', '时间 (秒)')
        combined_plot.addLegend()
        combined_plot.showGrid(x=True, y=True)

        # 启用鼠标交互
        combined_plot.setMouseEnabled(x=True, y=True)
        combined_plot.enableAutoRange()

        self.plot_layout.addWidget(combined_plot)
        self.plot_widgets['combined'] = {
            'widget': combined_plot,
            'fields': selected_fields,
            'curves': {}
        }

        # 为每个字段创建曲线
        for i, field_name in enumerate(selected_fields):
            color = self.colors[i % len(self.colors)]
            curve = combined_plot.plot(pen=pg.mkPen(color=color, width=2), name=field_name)
            self.plot_widgets['combined']['curves'][field_name] = curve

        # 创建独立视图
        for i, field_name in enumerate(selected_fields):
            plot_widget = pg.PlotWidget(title=field_name)
            plot_widget.setLabel('left', '数值')
            plot_widget.setLabel('bottom', '时间 (秒)')
            plot_widget.showGrid(x=True, y=True)
            plot_widget.setMouseEnabled(x=True, y=True)
            plot_widget.enableAutoRange()

            color = self.colors[i % len(self.colors)]
            curve = plot_widget.plot(pen=pg.mkPen(color=color, width=2))

            self.plot_layout.addWidget(plot_widget)
            self.plot_widgets[field_name] = {
                'widget': plot_widget,
                'curve': curve
            }

    def update_loop(self):
        """主更新循环"""
        all_new_data = self.receiver.get_latest_data()

        if all_new_data:
            self.update_plot_data()
            # 只用最后一帧数据更新表格
            self.update_table(all_new_data[-1]['parsed'])

    def update_table(self, parsed_data):
        """更新数据表格"""
        for name, value in parsed_data.items():
            if name in self.table_item_ids:
                row = self.table_item_ids[name]
                try:
                    hex_val = f"0x{int(value):X}" if isinstance(value, (int, float)) else "N/A"
                except (ValueError, TypeError):
                    hex_val = "N/A"

                self.data_table.item(row, 1).setText(str(value))
                self.data_table.item(row, 2).setText(hex_val)

    def update_plot_data(self):
        """更新绘图数据"""
        time_data = self.receiver.time_history

        if not time_data:
            return

        # 更新合并视图
        if 'combined' in self.plot_widgets:
            combined_info = self.plot_widgets['combined']
            for field_name in combined_info['fields']:
                if field_name in self.receiver.data_history:
                    data = self.receiver.data_history[field_name]
                    if len(data) == len(time_data):
                        curve = combined_info['curves'][field_name]
                        curve.setData(time_data, data)

        # 更新独立视图
        for field_name, plot_info in self.plot_widgets.items():
            if field_name == 'combined':
                continue

            if field_name in self.receiver.data_history:
                data = self.receiver.data_history[field_name]
                if len(data) == len(time_data):
                    plot_info['curve'].setData(time_data, data)

    def clear_data(self):
        """清除数据"""
        self.receiver._init_data_history()

        # 清除表格数据
        for name, row in self.table_item_ids.items():
            self.data_table.item(row, 1).setText("-")
            self.data_table.item(row, 2).setText("-")

        # 清除绘图数据
        self.update_plot_data()
        self.status_label.setText("数据已清除")

    def apply_time_window(self):
        """应用时间窗口设置"""
        new_window = self.time_window_spin.value()
        self.receiver.time_window = new_window
        self.status_label.setText(f"时间窗口已设置为 {new_window} 秒")

        # 立即清理超出新时间窗口的数据
        if self.receiver.time_history:
            current_time = self.receiver.time_history[-1]
            self.receiver._cleanup_old_data(current_time)
            self.update_plot_data()

    def update_port_list(self):
        """更新串口列表"""
        self.port_combo.clear()
        ports = [port.device for port in serial.tools.list_ports.comports()]
        if ports:
            self.port_combo.addItems(ports)
        else:
            self.port_combo.addItem("无可用串口")

    def start_receiver(self):
        """启动接收"""
        port = self.port_combo.currentText()
        if not port or "无可用串口" in port:
            QMessageBox.critical(self, "错误", "请选择有效串口")
            return

        baudrate = int(self.baudrate_combo.currentText())
        success, message = self.receiver.connect(port, baudrate)
        if not success:
            QMessageBox.critical(self, "连接错误", message)
            return

        self.receiver.start()
        self.status_label.setText("正在接收...")
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

    def stop_receiver(self):
        """停止接收"""
        self.receiver.stop()
        self.status_label.setText("已停止")
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

        # 询问是否保存数据
        if self.receiver.all_data_with_timestamp:
            reply = QMessageBox.question(self, "保存数据",
                f"检测到 {len(self.receiver.all_data_with_timestamp)} 条数据记录。\n是否保存为CSV文件？",
                QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.save_data_to_csv()

    def save_data_to_csv(self):
        """保存数据到CSV文件"""
        if not self.receiver.all_data_with_timestamp:
            QMessageBox.warning(self, "警告", "没有数据可保存！")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"sensor_data_{timestamp}.csv"

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存传感器数据", default_filename, "CSV文件 (*.csv)")

        if file_path:
            success, message = self.receiver.save_all_data_to_csv(file_path)
            if success:
                QMessageBox.information(self, "保存成功", message)
                self.status_label.setText(f"数据已保存到: {os.path.basename(file_path)}")
            else:
                QMessageBox.critical(self, "保存失败", message)
