import sys
import os
import time
import struct
import math
import threading
import collections
import serial
import serial.tools.list_ports

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import pyqtgraph as pg

# 导入协议解析器和信号生成器
from protocol_parser import ProtocolParser
from signal_generator import SIGNAL_GENERATORS, SIGNAL_PARAM_DEFINITIONS, USER_SELECTABLE_SIGNALS

# 配置
BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]
DEFAULT_PROTOCOL_FILE = "protocol_definition.csv"

protocol_parser = ProtocolParser()
if os.path.exists(DEFAULT_PROTOCOL_FILE):
    protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)

class Protocol422Sender:
    """422通讯发送端核心逻辑类"""
    def __init__(self, port='COM1', baudrate=19200, timeout=1, send_interval=0.005):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.send_interval = send_interval
        self.ser = None
        self.running = False
        self.frame_counter = 0
        self.protocol_structure = protocol_parser.get_protocol_structure()
        self.signal_params = {}
        self.command_fields = []
        
        self.start_time = 0.0
        self.simulation_time = 0.0
        
        self.reset_state = "idle"  # "idle", "resetting", "zeroing"
        self.reset_start_time = 0.0
        self.last_values_before_reset = {}
        self.original_types_before_reset = {}
        
        self.update_protocol_structure()

    def update_protocol_structure(self):
        self.protocol_structure = protocol_parser.get_protocol_structure()
        self.command_fields = [f['name'] for f in self.protocol_structure if '指令' in f['name']]
        self._init_signal_params()
        print("发送端协议结构已更新。")

    def _init_signal_params(self):
        self.signal_params.clear()
        for field_name in self.command_fields:
            self.signal_params[field_name] = {'enabled': False, 'type': "不发送", 'params': {}}

    def initiate_reset(self):
        if self.reset_state != "idle":
            return False

        was_running = self.running
        if self.running:
            self.stop_sending()

        self.last_values_before_reset = self.get_current_signal_values(self.simulation_time)
        self.original_types_before_reset.clear()

        any_signal_to_reset = any(self.signal_params[name]['enabled'] for name in self.command_fields)

        if not any_signal_to_reset and not was_running:
            self.reset()
            return "simple_reset"

        if not any_signal_to_reset and was_running:
            self.finalize_reset()
            return "already_zero"

        self.reset_state = "resetting"
        for name in self.command_fields:
            if self.signal_params[name]['enabled']:
                self.original_types_before_reset[name] = self.signal_params[name]['type']
                self.signal_params[name]['type'] = "_平滑衰减"
                self.signal_params[name]['params'] = {"start_value": self.last_values_before_reset.get(name, 0)}

        self.reset_start_time = time.monotonic()
        self.start_sending()
        return "smooth_reset"

    def reset(self):
        self.simulation_time = 0.0
        self.start_time = 0.0
        self.frame_counter = 0
        print("发送端状态已复位。")

    def _check_reset_complete(self, t_reset):
        if self.reset_state != "resetting":
            return True
        if t_reset > 2.0:
            return True

        current_values = self.get_current_signal_values(self.simulation_time)
        return all(abs(current_values.get(name, 0)) < 1 for name in self.original_types_before_reset.keys())

    def finalize_reset(self):
        self.reset_state = "zeroing"
        for name, original_type in self.original_types_before_reset.items():
            if name in self.signal_params:
                self.signal_params[name]['type'] = original_type
                self.signal_params[name]['params'] = SIGNAL_PARAM_DEFINITIONS[original_type].copy()

    def connect(self):
        try:
            self.ser = serial.Serial(port=self.port, baudrate=self.baudrate, timeout=self.timeout)
            return True, f"已连接到 {self.port}"
        except serial.SerialException as e:
            return False, f"串口连接失败: {e}"

    def disconnect(self):
        if self.ser and self.ser.is_open:
            self.ser.close()
        self.running = False
        return True, "已断开串口连接"

    def create_frame(self, t):
        if not self.protocol_structure:
            return bytearray()

        last_field = self.protocol_structure[-1]
        total_bytes = last_field['byte_indices'][-1] + 1
        frame = bytearray(total_bytes)

        for field in self.protocol_structure:
            field_name, value = field['name'], 0
            if field_name == '帧头':
                struct.pack_into('<H', frame, 0, 0x55AA)
                continue

            if field_name in self.command_fields:
                config = self.signal_params.get(field_name)
                if self.reset_state == "resetting" and field_name in self.original_types_before_reset:
                    t_signal = time.monotonic() - self.reset_start_time
                    generator_func = SIGNAL_GENERATORS.get(config['type'])
                    if generator_func:
                        value = generator_func(config['params'], t_signal)
                elif self.reset_state == "zeroing" and config.get('enabled'):
                    value = 0
                elif self.reset_state == "idle" and config and config.get('enabled'):
                    generator_func = SIGNAL_GENERATORS.get(config['type'])
                    if generator_func:
                        value = generator_func(config['params'], t)
            elif field_name == '帧计数':
                value = self.frame_counter
                self.frame_counter = (self.frame_counter + 1) % 65536
            elif field_name == '校验和':
                continue

            start_idx, data_type = field['byte_indices'][0], field['type']
            try:
                value = int(value)
                if data_type == 'int16':
                    struct.pack_into('<h', frame, start_idx, value)
                elif data_type == 'uint16':
                    struct.pack_into('<H', frame, start_idx, value)
                elif data_type == 'int8':
                    struct.pack_into('<b', frame, start_idx, value)
                elif data_type == 'uint8':
                    struct.pack_into('<B', frame, start_idx, value)
            except (struct.error, ValueError):
                pass

        checksum_field = protocol_parser.get_field_by_name('校验和')
        if checksum_field:
            checksum_idx = checksum_field['byte_indices'][0]
            checksum = 0
            for i in range(2, checksum_idx):
                checksum ^= frame[i]
            frame[checksum_idx] = checksum

        return frame

    def send_frame(self, t):
        if not self.ser or not self.ser.is_open:
            return
        try:
            self.ser.write(self.create_frame(t))
        except serial.SerialException as e:
            print(f"串口写入错误: {e}")
            self.stop_sending()

    def start_sending(self):
        if self.running:
            return False, "已在发送中"
        if not self.ser or not self.ser.is_open:
            success, message = self.connect()
            if not success:
                return False, message

        self.start_time = time.monotonic()
        self.running = True
        self.send_thread = threading.Thread(target=self._send_loop, daemon=True)
        self.send_thread.start()
        return True, "开始发送数据"

    def stop_sending(self):
        self.running = False
        if hasattr(self, 'send_thread') and threading.current_thread() != self.send_thread:
            self.send_thread.join(timeout=1.0)
        self.reset_state = "idle"
        return True, "停止发送数据"

    def _send_loop(self):
        while self.running:
            loop_start_time = time.monotonic()

            if self.reset_state == "resetting":
                t_reset = loop_start_time - self.reset_start_time
                self.send_frame(t_reset)
                if self._check_reset_complete(t_reset):
                    self.finalize_reset()
            else:
                if self.reset_state == "idle":
                    self.simulation_time = loop_start_time - self.start_time
                self.send_frame(self.simulation_time)

            elapsed = time.monotonic() - loop_start_time
            sleep_duration = self.send_interval - elapsed
            if sleep_duration > 0:
                time.sleep(sleep_duration)

    def get_current_signal_values(self, t):
        values = {}
        for field_name in self.command_fields:
            config = self.signal_params.get(field_name)
            if self.reset_state == "resetting" and field_name in self.original_types_before_reset:
                t_reset = time.monotonic() - self.reset_start_time
                values[field_name] = SIGNAL_GENERATORS["_平滑衰减"](config['params'], t_reset)
            elif self.reset_state == "zeroing" and config.get('enabled'):
                values[field_name] = 0
            elif self.reset_state == "idle" and config and config.get('enabled', False):
                generator_func = SIGNAL_GENERATORS.get(config['type'])
                if generator_func:
                    values[field_name] = generator_func(config['params'], t)
            else:
                values[field_name] = 0
        return values


class SenderWidget(QWidget):
    """发送端界面组件"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.sender = Protocol422Sender()
        self.command_widgets = {}
        self.plot_data = {}
        self.plot_history_size = 100
        self.colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
        
        self.init_ui()
        self.update_port_list()
        self.create_all_panels()
        self.init_data_table()
        
        # 定时器用于更新绘图
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_plots)
        self.update_timer.start(50)  # 50ms更新一次

    def init_ui(self):
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_group = QGroupBox("控制面板")
        control_layout = QHBoxLayout(control_group)
        
        # 串口设置
        settings_group = QGroupBox("串口设置")
        settings_layout = QGridLayout(settings_group)
        
        settings_layout.addWidget(QLabel("串口:"), 0, 0)
        self.port_combo = QComboBox()
        settings_layout.addWidget(self.port_combo, 0, 1)
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.update_port_list)
        settings_layout.addWidget(refresh_btn, 0, 2)
        
        settings_layout.addWidget(QLabel("波特率:"), 1, 0)
        self.baudrate_combo = QComboBox()
        self.baudrate_combo.addItems([str(b) for b in BAUDRATES])
        self.baudrate_combo.setCurrentText("19200")
        settings_layout.addWidget(self.baudrate_combo, 1, 1)
        
        settings_layout.addWidget(QLabel("周期(s):"), 2, 0)
        self.send_interval_spin = QDoubleSpinBox()
        self.send_interval_spin.setRange(0.001, 10.0)
        self.send_interval_spin.setValue(0.05)
        self.send_interval_spin.setDecimals(3)
        settings_layout.addWidget(self.send_interval_spin, 2, 1)
        
        apply_settings_btn = QPushButton("应用串口设置")
        apply_settings_btn.clicked.connect(self.apply_serial_settings)
        settings_layout.addWidget(apply_settings_btn, 3, 0, 1, 3)
        
        control_layout.addWidget(settings_group)
        
        # 主控制按钮
        main_control_group = QGroupBox("主控制")
        main_control_layout = QHBoxLayout(main_control_group)
        
        self.start_btn = QPushButton("开始")
        self.start_btn.clicked.connect(self.start_sender)
        main_control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_sender)
        self.stop_btn.setEnabled(False)
        main_control_layout.addWidget(self.stop_btn)
        
        self.reset_btn = QPushButton("复位")
        self.reset_btn.clicked.connect(self.reset_sender)
        main_control_layout.addWidget(self.reset_btn)
        
        clear_btn = QPushButton("清除数据显示")
        clear_btn.clicked.connect(self.clear_data_display)
        main_control_layout.addWidget(clear_btn)
        
        control_layout.addWidget(main_control_group)
        
        layout.addWidget(control_group)
        
        # 主要内容区域
        main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧面板 - 当前帧数据
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        data_group = QGroupBox("当前帧数据")
        data_layout = QVBoxLayout(data_group)
        
        self.data_table = QTableWidget()
        self.data_table.setColumnCount(3)
        self.data_table.setHorizontalHeaderLabels(["字段", "数值", "十六进制"])
        self.data_table.horizontalHeader().setStretchLastSection(True)
        data_layout.addWidget(self.data_table)
        
        left_layout.addWidget(data_group)
        main_splitter.addWidget(left_panel)
        
        # 右侧面板 - 信号控制和绘图
        self.right_scroll = QScrollArea()
        self.right_widget = QWidget()
        self.right_layout = QVBoxLayout(self.right_widget)
        self.right_scroll.setWidget(self.right_widget)
        self.right_scroll.setWidgetResizable(True)
        
        main_splitter.addWidget(self.right_scroll)
        main_splitter.setSizes([250, 650])  # 调整比例，与接收端保持一致
        
        layout.addWidget(main_splitter)
        
        # 状态栏
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
        
        # 用于存储表格行ID的字典
        self.table_item_ids = {}

    def create_all_panels(self):
        """创建所有信号控制面板"""
        # 清除现有面板
        for i in reversed(range(self.right_layout.count())):
            item = self.right_layout.itemAt(i)
            if item.widget():
                item.widget().setParent(None)
        self.command_widgets.clear()
        self.plot_data.clear()

        for field_name in self.sender.command_fields:
            if field_name not in self.plot_data:
                self.plot_data[field_name] = collections.deque(maxlen=self.plot_history_size)

            # 创建信号控制面板
            panel = QGroupBox(field_name)
            panel_layout = QVBoxLayout(panel)

            # 控制区域
            ctrl_widget = QWidget()
            ctrl_layout = QHBoxLayout(ctrl_widget)

            # 启用复选框
            enable_checkbox = QCheckBox("启用")
            enable_checkbox.setChecked(self.sender.signal_params[field_name]['enabled'])
            enable_checkbox.stateChanged.connect(lambda state, name=field_name: self.apply_signal_params(name))
            ctrl_layout.addWidget(enable_checkbox)

            # 信号类型选择
            ctrl_layout.addWidget(QLabel("信号:"))
            signal_combo = QComboBox()
            signal_combo.addItems(USER_SELECTABLE_SIGNALS)
            signal_combo.setCurrentText(self.sender.signal_params[field_name]['type'])
            signal_combo.currentTextChanged.connect(lambda text, name=field_name: self.on_signal_type_change(name))
            ctrl_layout.addWidget(signal_combo)

            # 参数输入区域
            param_widget = QWidget()
            param_layout = QHBoxLayout(param_widget)
            ctrl_layout.addWidget(param_widget)

            # 应用按钮
            apply_btn = QPushButton("应用")
            apply_btn.clicked.connect(lambda checked, name=field_name: self.apply_signal_params(name))
            ctrl_layout.addWidget(apply_btn)

            panel_layout.addWidget(ctrl_widget)

            # 绘图区域
            plot_widget = pg.PlotWidget(title=f"{field_name} 实时波形")
            plot_widget.setLabel('left', '数值')
            plot_widget.setLabel('bottom', '采样点')
            plot_widget.showGrid(x=True, y=True)
            plot_widget.setMouseEnabled(x=True, y=True)
            plot_widget.enableAutoRange()

            color = self.colors[len(self.command_widgets) % len(self.colors)]
            curve = plot_widget.plot(pen=pg.mkPen(color=color, width=2))

            panel_layout.addWidget(plot_widget)

            # 存储组件引用
            self.command_widgets[field_name] = {
                'panel': panel,
                'enable_checkbox': enable_checkbox,
                'signal_combo': signal_combo,
                'param_widget': param_widget,
                'param_layout': param_layout,
                'apply_btn': apply_btn,
                'plot_widget': plot_widget,
                'curve': curve,
                'param_inputs': {}
            }

            self.create_param_inputs(field_name)
            self.right_layout.addWidget(panel)

    def create_param_inputs(self, field_name):
        """创建参数输入控件"""
        widgets = self.command_widgets[field_name]
        param_layout = widgets['param_layout']

        # 清除现有控件
        for i in reversed(range(param_layout.count())):
            param_layout.itemAt(i).widget().setParent(None)
        widgets['param_inputs'].clear()

        signal_type = widgets['signal_combo'].currentText()
        param_defs = SIGNAL_PARAM_DEFINITIONS.get(signal_type, {})
        current_params = self.sender.signal_params[field_name]['params']

        for param_name, default_value in param_defs.items():
            param_layout.addWidget(QLabel(f"{param_name}:"))

            spin_box = QDoubleSpinBox()
            spin_box.setRange(-99999, 99999)
            spin_box.setDecimals(3)
            spin_box.setValue(current_params.get(param_name, default_value))
            param_layout.addWidget(spin_box)

            widgets['param_inputs'][param_name] = spin_box

    def on_signal_type_change(self, field_name):
        """信号类型改变时的处理"""
        widgets = self.command_widgets[field_name]
        new_type = widgets['signal_combo'].currentText()
        self.sender.signal_params[field_name]['type'] = new_type
        self.sender.signal_params[field_name]['params'] = SIGNAL_PARAM_DEFINITIONS[new_type].copy()
        self.create_param_inputs(field_name)
        self.apply_signal_params(field_name)

    def apply_serial_settings(self):
        """应用串口设置"""
        try:
            interval = self.send_interval_spin.value()
            self.sender.send_interval = max(interval, 0.001)
            self.sender.baudrate = int(self.baudrate_combo.currentText())
            self.status_label.setText("串口设置已应用")
            return True
        except Exception as e:
            QMessageBox.critical(self, "输入错误", f"请输入有效的数值: {e}")
            return False

    def apply_signal_params(self, field_name):
        """应用信号参数"""
        try:
            widgets = self.command_widgets[field_name]
            self.sender.signal_params[field_name]['enabled'] = widgets['enable_checkbox'].isChecked()

            params = self.sender.signal_params[field_name]['params']
            for param_name, spin_box in widgets['param_inputs'].items():
                params[param_name] = spin_box.value()

            self.status_label.setText(f"'{field_name}' 参数已应用")
            return True
        except Exception as e:
            QMessageBox.critical(self, "输入错误", f"'{field_name}' 的参数无效: {e}")
            return False

    def init_data_table(self):
        """初始化数据表格"""
        self.data_table.setRowCount(len(self.sender.protocol_structure))
        self.table_item_ids.clear()

        for i, field in enumerate(self.sender.protocol_structure):
            name = field['name']
            self.data_table.setItem(i, 0, QTableWidgetItem(name))
            self.data_table.setItem(i, 1, QTableWidgetItem("-"))
            self.data_table.setItem(i, 2, QTableWidgetItem("-"))
            self.table_item_ids[name] = i

    def update_data_table(self):
        """更新数据表格显示当前发送的数据"""
        if not self.sender.running:
            return

        # 获取当前时间的信号值
        current_values = self.sender.get_current_signal_values(self.sender.simulation_time)

        # 创建完整的帧数据
        frame_data = {}
        for field in self.sender.protocol_structure:
            name = field['name']
            if name == '帧头':
                frame_data[name] = 0xAA55
            elif name == '校验和':
                frame_data[name] = 0
            elif name in current_values:
                frame_data[name] = current_values[name]
            else:
                frame_data[name] = 0

        # 更新表格显示
        for name, value in frame_data.items():
            if name in self.table_item_ids:
                row = self.table_item_ids[name]
                try:
                    if isinstance(value, (int, float)):
                        int_value = int(value)
                        hex_val = f"0x{int_value:X}" if int_value >= 0 else f"-0x{abs(int_value):X}"
                        self.data_table.item(row, 1).setText(str(int_value))
                        self.data_table.item(row, 2).setText(hex_val)
                    else:
                        self.data_table.item(row, 1).setText(str(value))
                        self.data_table.item(row, 2).setText("N/A")
                except (ValueError, TypeError):
                    self.data_table.item(row, 1).setText(str(value))
                    self.data_table.item(row, 2).setText("N/A")

    def clear_data_display(self):
        """手动清除数据显示"""
        for name, row in self.table_item_ids.items():
            self.data_table.item(row, 1).setText("-")
            self.data_table.item(row, 2).setText("-")

    def update_plots(self):
        """更新绘图"""
        current_values = self.sender.get_current_signal_values(self.sender.simulation_time)

        if self.sender.running:
            for name, value in current_values.items():
                if name in self.plot_data:
                    self.plot_data[name].append(value)

        for name, value in current_values.items():
            config = self.sender.signal_params.get(name)

            if config and (config.get('enabled') or (
                    self.sender.reset_state == "resetting" and name in self.sender.original_types_before_reset)):
                widgets = self.command_widgets.get(name)
                if widgets:
                    curve = widgets['curve']
                    plot_widget = widgets['plot_widget']

                    # 更新曲线数据
                    y_data = list(self.plot_data[name])
                    x_data = list(range(len(y_data)))
                    curve.setData(x_data, y_data)

                    # 更新标题显示当前值
                    plot_widget.setTitle(f"{name} 实时波形 (当前值: {value:.2f})")

        # 更新数据表格
        self.update_data_table()

    def start_sender(self):
        """启动发送"""
        if not self.apply_serial_settings():
            return

        for field_name in self.sender.command_fields:
            if not self.apply_signal_params(field_name):
                return

        port = self.port_combo.currentText()
        if not port or "无可用串口" in port:
            QMessageBox.critical(self, "错误", "请选择一个有效的串口。")
            return

        self.sender.port = port
        success, message = self.sender.start_sending()
        if success:
            self.status_label.setText("正在发送...")
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.reset_btn.setEnabled(True)
        else:
            QMessageBox.critical(self, "启动错误", message)

    def stop_sender(self):
        """停止发送"""
        self.sender.stop_sending()
        self.status_label.setText("已停止发送")
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.reset_btn.setEnabled(True)

    def reset_sender(self):
        """复位发送端"""
        if self.sender.reset_state != "idle":
            return

        reset_action = self.sender.initiate_reset()

        if reset_action == "smooth_reset":
            self.status_label.setText("正在平滑复位...")
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            self.reset_btn.setEnabled(False)
            QTimer.singleShot(100, self.check_reset_gui_finalize)
        elif reset_action == "simple_reset":
            self.status_label.setText("已复位 (无活动信号)")
            for name in self.plot_data:
                self.plot_data[name].clear()
        elif reset_action == "already_zero":
            self.status_label.setText("已在零位，停止发送")
            self.stop_sender()

    def check_reset_gui_finalize(self):
        """检查复位GUI完成状态"""
        if self.sender.reset_state == "zeroing":
            self.status_label.setText("归零完成，持续发送0。请手动停止。")
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.reset_btn.setEnabled(False)
        elif self.sender.reset_state == "resetting":
            QTimer.singleShot(100, self.check_reset_gui_finalize)
        else:
            self.stop_sender()

    def update_port_list(self):
        """更新串口列表"""
        self.port_combo.clear()
        ports = [port.device for port in serial.tools.list_ports.comports()]
        if ports:
            self.port_combo.addItems(ports)
        else:
            self.port_combo.addItem("无可用串口")
