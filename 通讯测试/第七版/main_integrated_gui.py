#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
422通讯测试整合界面
集成了接收端和发送端功能，使用PyQt5和pyqtgraph实现高效绘图
"""

import sys
import os
import locale
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import pyqtgraph as pg

# 导入自定义组件
from receiver_widget import ReceiverWidget
from sender_widget import SenderWidget
from protocol_parser import ProtocolParser

# 基本配置
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass

# 设置pyqtgraph的默认配置
pg.setConfigOptions(antialias=True)  # 启用抗锯齿
pg.setConfigOption('background', 'w')  # 白色背景
pg.setConfigOption('foreground', 'k')  # 黑色前景

DEFAULT_PROTOCOL_FILE = "protocol_definition.csv"


class ProtocolManager:
    """简化的协议管理器"""
    def __init__(self):
        self.current_protocol_file = DEFAULT_PROTOCOL_FILE

    def get_current_protocol_file(self):
        return self.current_protocol_file


class ProtocolEditorDialog(QDialog):
    """协议编辑器对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.protocol_parser = ProtocolParser()
        self.protocol_manager = ProtocolManager()
        self.init_ui()
        self.load_current_protocol()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🔧 协议编辑器")
        self.setModal(True)
        self.resize(900, 700)

        layout = QVBoxLayout(self)

        # 标题栏
        title_label = QLabel("协议字段定义编辑器")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2196F3;
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(33, 150, 243, 0.1), stop:1 rgba(33, 150, 243, 0.05));
                border-radius: 6px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 工具栏
        toolbar = QHBoxLayout()

        load_btn = QPushButton("📂 加载协议")
        load_btn.clicked.connect(self.load_protocol)
        toolbar.addWidget(load_btn)

        save_btn = QPushButton("💾 保存协议")
        save_btn.clicked.connect(self.save_protocol)
        toolbar.addWidget(save_btn)

        toolbar.addStretch()

        add_btn = QPushButton("➕ 添加字段")
        add_btn.clicked.connect(self.add_field)
        toolbar.addWidget(add_btn)

        delete_btn = QPushButton("🗑️ 删除字段")
        delete_btn.setProperty("class", "danger")
        delete_btn.clicked.connect(self.delete_field)
        toolbar.addWidget(delete_btn)

        layout.addLayout(toolbar)

        # 协议表格
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(["字节序号", "名称", "单位", "数据类型", "描述"])

        # 设置列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.Stretch)

        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        layout.addWidget(self.table)

        # 说明文本
        help_text = QLabel("""
💡 使用说明：
• 字节序号：支持单个字节(如"3")或范围(如"3-4")
• 名称：字段的中文名称，如"X轴指令"、"帧头"等
• 单位：数据单位，如"mm"、"度"等
• 数据类型：支持 int8, uint8, int16, uint16
• 描述：字段的详细说明(可选)
        """)
        help_text.setStyleSheet("""
            QLabel {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
                font-size: 11px;
                color: #6c757d;
            }
        """)
        layout.addWidget(help_text)

        # 按钮栏
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept_changes)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def load_current_protocol(self):
        """加载当前协议"""
        try:
            if os.path.exists(DEFAULT_PROTOCOL_FILE):
                self.protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)
                self.update_table()
            else:
                # 创建默认协议
                self.create_default_protocol()
        except Exception as e:
            QMessageBox.warning(self, "加载错误", f"加载协议文件失败: {e}")

    def create_default_protocol(self):
        """创建默认协议"""
        default_fields = [
            ("1-2", "帧头", "", "uint16", "固定为0xAA55"),
            ("3-4", "X轴指令", "mm", "int16", "X轴位置指令"),
            ("5-6", "Y轴指令", "mm", "int16", "Y轴位置指令"),
            ("7-8", "Z轴指令", "mm", "int16", "Z轴位置指令"),
            ("9-10", "帧计数", "", "uint16", "帧序号计数"),
            ("11", "校验和", "", "uint8", "XOR校验")
        ]

        self.table.setRowCount(len(default_fields))
        for i, (byte_range, name, unit, data_type, desc) in enumerate(default_fields):
            self.table.setItem(i, 0, QTableWidgetItem(byte_range))
            self.table.setItem(i, 1, QTableWidgetItem(name))
            self.table.setItem(i, 2, QTableWidgetItem(unit))
            self.table.setItem(i, 3, QTableWidgetItem(data_type))
            self.table.setItem(i, 4, QTableWidgetItem(desc))

    def update_table(self):
        """更新表格显示"""
        structure = self.protocol_parser.get_protocol_structure()
        self.table.setRowCount(len(structure))

        for i, field in enumerate(structure):
            # 字节序号
            byte_indices = field['byte_indices']
            if len(byte_indices) == 1:
                byte_range = str(byte_indices[0] + 1)  # 转换为1-based
            else:
                byte_range = f"{byte_indices[0] + 1}-{byte_indices[-1] + 1}"

            self.table.setItem(i, 0, QTableWidgetItem(byte_range))
            self.table.setItem(i, 1, QTableWidgetItem(field['name']))
            self.table.setItem(i, 2, QTableWidgetItem(field.get('unit', '')))
            self.table.setItem(i, 3, QTableWidgetItem(field['type']))
            self.table.setItem(i, 4, QTableWidgetItem(field.get('description', '')))

    def add_field(self):
        """添加字段"""
        row = self.table.rowCount()
        self.table.insertRow(row)

        # 设置默认值
        self.table.setItem(row, 0, QTableWidgetItem(""))
        self.table.setItem(row, 1, QTableWidgetItem("新字段"))
        self.table.setItem(row, 2, QTableWidgetItem(""))
        self.table.setItem(row, 3, QTableWidgetItem("uint8"))
        self.table.setItem(row, 4, QTableWidgetItem(""))

        # 选中新行
        self.table.selectRow(row)

    def delete_field(self):
        """删除字段"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "确认删除",
                f"确定要删除第 {current_row + 1} 行字段吗？",
                QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.table.removeRow(current_row)

    def load_protocol(self):
        """加载协议文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载协议文件", "", "CSV文件 (*.csv)")

        if file_path:
            try:
                self.protocol_parser.load_from_csv(file_path)
                self.update_table()
                QMessageBox.information(self, "加载成功", f"已加载协议文件: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "加载失败", f"加载协议文件失败: {e}")

    def save_protocol(self):
        """保存协议文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存协议文件", DEFAULT_PROTOCOL_FILE, "CSV文件 (*.csv)")

        if file_path:
            try:
                self.save_table_to_csv(file_path)
                QMessageBox.information(self, "保存成功", f"协议已保存到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存协议文件失败: {e}")

    def save_table_to_csv(self, file_path):
        """将表格数据保存为CSV"""
        import csv
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)

            # 写入表头
            headers = ["字节序号", "名称", "单位", "数据类型/长度", "描述"]
            writer.writerow(headers)

            # 写入数据
            for row in range(self.table.rowCount()):
                row_data = []
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    row_data.append(item.text() if item else "")
                writer.writerow(row_data)

    def accept_changes(self):
        """接受更改"""
        try:
            # 验证数据
            if not self.validate_table_data():
                return

            # 保存到默认文件
            self.save_table_to_csv(DEFAULT_PROTOCOL_FILE)

            # 重新加载协议解析器
            self.protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)

            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存协议失败: {e}")

    def validate_table_data(self):
        """验证表格数据"""
        for row in range(self.table.rowCount()):
            # 检查必填字段
            byte_range = self.table.item(row, 0)
            name = self.table.item(row, 1)
            data_type = self.table.item(row, 3)

            if not byte_range or not byte_range.text().strip():
                QMessageBox.warning(self, "验证失败", f"第 {row + 1} 行的字节序号不能为空")
                return False

            if not name or not name.text().strip():
                QMessageBox.warning(self, "验证失败", f"第 {row + 1} 行的名称不能为空")
                return False

            if not data_type or not data_type.text().strip():
                QMessageBox.warning(self, "验证失败", f"第 {row + 1} 行的数据类型不能为空")
                return False

            # 验证数据类型
            valid_types = ['int8', 'uint8', 'int16', 'uint16']
            if data_type.text().strip() not in valid_types:
                QMessageBox.warning(self, "验证失败",
                    f"第 {row + 1} 行的数据类型无效，支持的类型: {', '.join(valid_types)}")
                return False

        return True

class MainIntegratedGUI(QMainWindow):
    """主整合界面"""
    
    def __init__(self):
        super().__init__()
        self.protocol_parser = ProtocolParser()
        self.load_default_protocol()
        self.init_ui()
        self.setup_menu()
        
    def load_default_protocol(self):
        """加载默认协议文件"""
        if os.path.exists(DEFAULT_PROTOCOL_FILE):
            self.protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)
            print(f"已加载协议文件: {DEFAULT_PROTOCOL_FILE}")
        else:
            print(f"警告: 未找到默认协议定义文件 '{DEFAULT_PROTOCOL_FILE}'")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("🚀 422通讯测试整合界面 - 专业版")
        self.setGeometry(50, 50, 1600, 900)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(5)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建标题栏
        title_widget = self.create_title_bar()
        main_layout.addWidget(title_widget)

        # 创建主要内容区域 - 左右分布
        main_splitter = QSplitter(Qt.Horizontal)
        main_splitter.setHandleWidth(6)
        main_splitter.setStyleSheet("""
            QSplitter::handle {
                background: #dee2e6;
                border: 1px solid #adb5bd;
                border-radius: 2px;
            }
            QSplitter::handle:hover {
                background: #4CAF50;
            }
        """)

        # 直接添加接收端和发送端组件，不用额外的面板包装
        self.receiver_widget = ReceiverWidget()
        self.sender_widget = SenderWidget()

        main_splitter.addWidget(self.receiver_widget)
        main_splitter.addWidget(self.sender_widget)

        # 设置分割器比例 (接收端:发送端 = 1:1)
        main_splitter.setSizes([800, 800])
        main_layout.addWidget(main_splitter)

        # 创建状态栏
        self.status_bar = self.statusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-top: 1px solid #dee2e6;
                color: #495057;
                font-weight: bold;
                padding: 5px;
            }
        """)
        self.status_bar.showMessage("🟢 系统就绪 - 接收端和发送端已加载完成")

        # 设置样式
        self.setup_styles()

    def create_title_bar(self):
        """创建标题栏"""
        title_widget = QWidget()
        title_widget.setFixedHeight(45)
        title_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #2196F3);
                border-radius: 6px;
                margin-bottom: 3px;
            }
        """)

        layout = QHBoxLayout(title_widget)
        layout.setContentsMargins(15, 8, 15, 8)

        # 标题文本
        title_label = QLabel("📡 422通讯测试整合平台 📤")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                background: transparent;
            }
        """)
        layout.addWidget(title_label)

        layout.addStretch()

        # 版本信息
        version_label = QLabel("v1.0")
        version_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 11px;
                background: transparent;
                padding: 3px 8px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
            }
        """)
        layout.addWidget(version_label)

        return title_widget


    
    def setup_styles(self):
        """设置界面样式"""
        style = """
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 15px;
            background: white;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            color: #495057;
            font-size: 13px;
        }

        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4CAF50, stop:1 #45a049);
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
            margin: 3px;
            border-radius: 6px;
            min-width: 80px;
        }

        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #45a049, stop:1 #3d8b40);
            transform: translateY(-1px);
        }

        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3d8b40, stop:1 #2e7d32);
            transform: translateY(1px);
        }

        QPushButton:disabled {
            background: #cccccc;
            color: #666666;
        }

        /* 特殊按钮样式 */
        QPushButton[class="danger"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f44336, stop:1 #d32f2f);
        }

        QPushButton[class="danger"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #d32f2f, stop:1 #c62828);
        }

        QPushButton[class="warning"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ff9800, stop:1 #f57c00);
        }

        QPushButton[class="warning"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f57c00, stop:1 #ef6c00);
        }

        QPushButton[class="info"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2196F3, stop:1 #1976D2);
        }

        QPushButton[class="info"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1976D2, stop:1 #1565C0);
        }

        QComboBox, QSpinBox, QDoubleSpinBox, QLineEdit {
            padding: 8px 12px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            background: white;
            font-size: 12px;
            min-height: 20px;
        }

        QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QLineEdit:focus {
            border-color: #4CAF50;
            outline: none;
        }

        QComboBox::drop-down {
            border: none;
            width: 30px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #666;
            margin-right: 10px;
        }

        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-size: 12px;
        }

        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
        }

        QTableWidget::item:selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4CAF50, stop:1 #45a049);
            color: white;
        }

        QTableWidget QHeaderView::section {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
            padding: 8px;
            border: 1px solid #dee2e6;
            font-weight: bold;
            color: #495057;
        }

        QCheckBox {
            font-size: 12px;
            color: #495057;
            spacing: 8px;
        }

        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #dee2e6;
            border-radius: 4px;
            background: white;
        }

        QCheckBox::indicator:checked {
            background: #4CAF50;
            border-color: #4CAF50;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }

        QScrollArea {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: white;
        }

        QScrollBar:vertical {
            background: #f8f9fa;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background: #ced4da;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background: #adb5bd;
        }

        QLabel {
            color: #495057;
            font-size: 12px;
        }
        """
        self.setStyleSheet(style)
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        # 协议编辑器
        protocol_action = QAction('协议编辑器(&P)', self)
        protocol_action.setShortcut('Ctrl+P')
        protocol_action.setStatusTip('打开协议编辑器')
        protocol_action.triggered.connect(self.open_protocol_editor)
        file_menu.addAction(protocol_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.setStatusTip('退出应用程序')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')
        
        # 清除所有数据
        clear_all_action = QAction('清除所有数据(&C)', self)
        clear_all_action.setStatusTip('清除接收端和发送端的所有数据')
        clear_all_action.triggered.connect(self.clear_all_data)
        tools_menu.addAction(clear_all_action)
        
        # 停止所有操作
        stop_all_action = QAction('停止所有操作(&S)', self)
        stop_all_action.setShortcut('Ctrl+S')
        stop_all_action.setStatusTip('停止接收端和发送端的所有操作')
        stop_all_action.triggered.connect(self.stop_all_operations)
        tools_menu.addAction(stop_all_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 关于
        about_action = QAction('关于(&A)', self)
        about_action.setStatusTip('关于此应用程序')
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
        # 使用说明
        help_action = QAction('使用说明(&H)', self)
        help_action.setStatusTip('显示使用说明')
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)
    
    def open_protocol_editor(self):
        """打开协议编辑器"""
        # 检查是否有正在运行的操作
        if (hasattr(self.receiver_widget.receiver, 'running') and self.receiver_widget.receiver.running) or \
           (hasattr(self.sender_widget.sender, 'running') and self.sender_widget.sender.running):
            QMessageBox.warning(self, "提示", "请先停止所有正在运行的操作再编辑协议。")
            return

        # 创建协议编辑器对话框
        editor_dialog = ProtocolEditorDialog(self)
        if editor_dialog.exec_() == QDialog.Accepted:
            # 协议更新后刷新界面
            self.on_protocol_updated()

    def on_protocol_updated(self):
        """协议更新后的处理"""
        print("协议已更新，正在刷新界面...")

        # 更新接收端
        self.receiver_widget.receiver.update_protocol_structure()
        self.receiver_widget.rebuild_ui_from_protocol()

        # 更新发送端
        self.sender_widget.sender.update_protocol_structure()
        self.sender_widget.create_all_panels()
        self.sender_widget.init_data_table()

        self.status_bar.showMessage("🔄 协议已更新，界面已刷新")
        QMessageBox.information(self, "协议更新", "协议已成功更新，接收端和发送端已应用新协议！")
    
    def clear_all_data(self):
        """清除所有数据"""
        reply = QMessageBox.question(self, "确认", "确定要清除接收端和发送端的所有数据吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.receiver_widget.clear_data()
            self.sender_widget.clear_data_display()
            self.status_bar.showMessage("所有数据已清除")
    
    def stop_all_operations(self):
        """停止所有操作"""
        # 停止接收端
        if hasattr(self.receiver_widget.receiver, 'running') and self.receiver_widget.receiver.running:
            self.receiver_widget.stop_receiver()
        
        # 停止发送端
        if hasattr(self.sender_widget.sender, 'running') and self.sender_widget.sender.running:
            self.sender_widget.stop_sender()
        
        self.status_bar.showMessage("所有操作已停止")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "422通讯测试整合界面\n\n"
                         "版本: 1.0\n"
                         "基于PyQt5和pyqtgraph开发\n"
                         "提供高效的实时数据绘图和交互功能\n\n"
                         "特性:\n"
                         "• 实时数据接收和显示\n"
                         "• 多种信号生成和发送\n"
                         "• 高效的绘图性能\n"
                         "• 鼠标交互支持\n"
                         "• 数据保存功能")
    
    def show_help(self):
        """显示帮助对话框"""
        help_text = """
使用说明:

接收端:
1. 选择串口和波特率
2. 点击"启动接收"开始接收数据
3. 在左侧选择要绘图的数据字段
4. 实时查看数据图表和数值
5. 可以调整时间窗口大小
6. 停止后可选择保存数据为CSV文件

发送端:
1. 配置串口设置
2. 为每个指令字段选择信号类型
3. 调整信号参数
4. 点击"开始"发送数据
5. 实时查看发送的波形
6. 可以使用"复位"功能平滑停止信号

绘图交互:
• 鼠标滚轮: 缩放
• 鼠标拖拽: 平移
• 右键: 重置视图
• 鼠标悬停: 显示坐标值

快捷键:
• Ctrl+P: 打开协议编辑器
• Ctrl+S: 停止所有操作
• Ctrl+Q: 退出程序
        """
        
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("使用说明")
        msg_box.setText(help_text)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
    
    def closeEvent(self, event):
        """关闭事件处理"""
        reply = QMessageBox.question(self, "确认退出", "确定要退出程序吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            # 停止所有操作
            self.stop_all_operations()
            event.accept()
        else:
            event.ignore()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("422通讯测试整合界面")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("通讯测试工具")
    
    # 创建主窗口
    main_window = MainIntegratedGUI()
    main_window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
